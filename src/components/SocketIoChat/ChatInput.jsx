import React, { useState, useRef, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { Plus, ArrowUp, X, FileText, Loader2, Info, RefreshCw } from 'lucide-react';
import { uploadMultipleAttachments } from '@/utils/fileAPI';
import { useParams, useSearchParams } from 'next/navigation';
import { BootstrapTooltip } from '../UIComponents/ToolTip/Tooltip-material-ui';
import { FaStopCircle } from 'react-icons/fa';
import { getFigmaFiles, getFigmaJsonFiles } from '@/utils/FigmaAPI';
import { addFigmaFileV2, getFigmaProcessingStatus } from '@/api/figma';

// Image Preview Component
const ImagePreview = ({ imageUrl, isVisible, mousePosition }) => {
  if (!isVisible || !imageUrl) return null;

  return ReactDOM.createPortal(
    <div
      className="fixed pointer-events-none z-[10000]"
      style={{
        left: mousePosition.x + 10,
        top: mousePosition.y - 100,
        transform: mousePosition.x > window.innerWidth - 300 ? 'translateX(-100%)' : 'none'
      }}
    >
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 p-2 max-w-xs">
        <img
          src={imageUrl}
          alt="Figma frame preview"
          className="w-full h-auto max-h-48 rounded object-contain"
          onError={(e) => {
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'block';
          }}
        />
        <div className="hidden text-xs text-gray-500 text-center py-2">
          Preview not available
        </div>
      </div>
    </div>,
    document.body
  );
};

// Progress Modal Component
const ProgressModal = ({ isOpen, onClose, design, processingStatus, isLoadingStatus }) => {
  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 w-[425px] max-w-[90%] relative">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
        >
          <X className="h-5 w-5" />
        </button>
        
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Processing Status</h2>
          <div className="space-y-4">
            {isLoadingStatus ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
              </div>
            ) : processingStatus ? (
              <>
                <div className="space-y-3">
                  {/* Design Name */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Design:</span>
                    <span className="text-sm text-gray-600">{design?.name}</span>
                  </div>

                  {/* Status Badge */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      processingStatus.status === 'completed' ? 'bg-green-100 text-green-800' :
                      processingStatus.status === 'processing' ? 'bg-primary-100 text-primary-800' :
                      processingStatus.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {processingStatus.status.charAt(0).toUpperCase() + processingStatus.status.slice(1)}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{Math.round((processingStatus.completed_frames / processingStatus.total_frames) * 100) || 0}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          processingStatus.status === 'completed' ? 'bg-green-500' :
                          processingStatus.status === 'failed' ? 'bg-gray-300' : 'bg-primary'
                        }`}
                        style={{ width: `${(processingStatus.completed_frames / processingStatus.total_frames) * 100 || 0}%` }}
                      />
                    </div>
                  </div>

                  {/* Frame Details */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Total Frames:</span>
                      <span>{processingStatus.total_frames}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Completed Frames:</span>
                      <span>{processingStatus.completed_frames}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Failed Frames:</span>
                      <span className={processingStatus.failed_frames > 0 ? 'text-red-600' : 'text-gray-600'}>
                        {processingStatus.failed_frames}
                      </span>
                    </div>
                  </div>

                  {/* Error Message if any */}
                  {processingStatus.error_message && (
                    <div className="text-sm text-red-600 bg-red-50 p-3 rounded">
                      {processingStatus.error_message}
                    </div>
                  )}

                  {/* Last Updated */}
                  <div className="text-xs text-gray-500">
                    Last updated: {new Date(processingStatus.time_updated).toLocaleString()}
                  </div>
                </div>
              </>
            ) : (
              <p className="text-gray-600">No status information available</p>
            )}
          </div>
          <div className="flex justify-end pt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

// Add Figma Design Modal
const AddFigmaDesignModal = ({ isOpen, onClose, onSubmit, isLoading }) => {
  const [designName, setDesignName] = useState('');
  const [figmaLink, setFigmaLink] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (designName.trim() && figmaLink.trim()) {
      onSubmit(designName.trim(), figmaLink.trim());
      setDesignName('');
      setFigmaLink('');
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setDesignName('');
      setFigmaLink('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 w-[500px] max-w-[90%] relative">
        <button
          onClick={handleClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
          disabled={isLoading}
        >
          <X className="h-5 w-5" />
        </button>
        
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Add Figma Design</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="designName" className="block text-sm font-medium text-gray-700 mb-1">
                Design Name
              </label>
              <input
                id="designName"
                type="text"
                value={designName}
                onChange={(e) => setDesignName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Enter design name"
                required
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label htmlFor="figmaLink" className="block text-sm font-medium text-gray-700 mb-1">
                Figma Link
              </label>
              <input
                id="figmaLink"
                type="url"
                value={figmaLink}
                onChange={(e) => setFigmaLink(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="https://www.figma.com/..."
                required
                disabled={isLoading}
              />
            </div>
            
            <div className="flex justify-end space-x-2 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className={`px-4 py-2 ${
                  isLoading
                    ? 'bg-primary-300 cursor-not-allowed'
                    : 'bg-primary hover:bg-primary-600'
                } text-white rounded-md flex items-center gap-2`}
                disabled={isLoading}
              >
                {isLoading && <Loader2 size={16} className="animate-spin" />}
                {isLoading ? 'Adding...' : 'Add Design'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>,
    document.body
  );
};

const useClickOutside = (ref, handler, exceptRef = null) => {
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        ref.current &&
        !ref.current.contains(event.target) &&
        !(exceptRef && exceptRef.current && exceptRef.current.contains(event.target))
      ) {
        handler();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler, exceptRef]);
};

const FileAttachmentPopup = ({
  files,
  onRemoveFile,
  onClearFiles,
  onAddMoreFiles,
  onClose,
  triggerButtonRef,
  isUploading,
  figmaFiles,
  onSelectFigmaFile,
  selectedFigmaFiles,
  isLoadingFigma,
  expandedFigmaDesigns,
  onToggleFigmaDesign,
  figmaJsonFiles,
  onSelectFigmaJson,
  selectedFigmaJsonFiles,
  isLoadingFigmaJson,
  onClearFigmaFiles,
  onAddFigmaDesign,
  processingDesigns,
  onCheckStatus,
  jsonProcessingStatus
}) => {
  const popupRef = useRef(null);
  const firstButtonRef = useRef(null);
  const [activeTab, setActiveTab] = useState('files');
  const [hoveredImage, setHoveredImage] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  
  // Handle click outside
  useClickOutside(popupRef, onClose, triggerButtonRef);

  // Handle mouse movement for image preview
  const handleMouseMove = (e) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  // Handle mouse enter for JSON file with image
  const handleJsonFileMouseEnter = (jsonFile) => {
    if (jsonFile.image_url) {
      setHoveredImage(jsonFile.image_url);
    }
  };

  // Handle mouse leave for JSON file
  const handleJsonFileMouseLeave = () => {
    setHoveredImage(null);
  };

  // Focus management
  useEffect(() => {
    // Store the previously focused element
    const previousFocus = document.activeElement;

    // Focus the first interactive element in the popup
    if (firstButtonRef.current) {
      firstButtonRef.current.focus();
    }

    // Cleanup function to restore focus when component unmounts
    return () => {
      if (triggerButtonRef.current) {
        triggerButtonRef.current.focus();
      } else if (previousFocus) {
        previousFocus.focus();
      }
    };
  }, []);

  // Find the position of the trigger button to position the popup
  const calculatePosition = () => {
    if (!triggerButtonRef.current) return { top: '60px', left: '20px' };

    const rect = triggerButtonRef.current.getBoundingClientRect();
    return {
      bottom: `${window.innerHeight - rect.top + 10}px`,
      left: `${rect.left}px`
    };
  };

  const position = calculatePosition();

  return ReactDOM.createPortal(
    <div
      className="fixed inset-0 z-[9999] pointer-events-none"
      aria-modal="true"
      role="dialog"
      aria-label="File attachments and Figma designs"
      onMouseMove={handleMouseMove}
    >
      <div
        ref={popupRef}
        className="absolute pointer-events-auto bg-white rounded-lg shadow-2xl border border-gray-300"
        style={{
          bottom: position.bottom,
          left: position.left,
          width: '350px',
          zIndex: 9999
        }}
        tabIndex={-1}
      >
        {/* Header with tabs */}
        <div className="border-b border-gray-200 bg-gray-50">
          <div className="flex">
            <button
              ref={firstButtonRef}
              onClick={() => setActiveTab('files')}
              className={`flex-1 px-4 py-3 text-sm font-medium ${activeTab === 'files'
                  ? 'text-orange-600 border-b-2 border-orange-600 bg-white'
                  : 'text-gray-600 hover:text-gray-800'
                }`}
            >
              Files ({files.length})
            </button>
            <button
              onClick={() => setActiveTab('figma')}
              className={`flex-1 px-4 py-3 text-sm font-medium ${activeTab === 'figma'
                  ? 'text-orange-600 border-b-2 border-orange-600 bg-white'
                  : 'text-gray-600 hover:text-gray-800'
                }`}
            >
              Figma ({selectedFigmaJsonFiles.length})
            </button>
          </div>
        </div>

        {/* Content area */}
        <div className="max-h-80 overflow-y-auto">
          {activeTab === 'files' && (
            <div>
              {/* Files header */}
              <div className="flex justify-between items-center p-3 border-b border-gray-100">
                <h4 className="text-sm font-medium text-gray-700">Attached Files</h4>
                <div className="flex gap-2">
                  <button
                    onClick={onAddMoreFiles}
                    className="text-orange-500 hover:text-orange-700 text-sm font-medium"
                    disabled={isUploading}
                  >
                    + Add
                  </button>
                  {files.length > 0 && (
                    <button
                      onClick={onClearFiles}
                      className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                      disabled={isUploading}
                    >
                      Clear
                    </button>
                  )}
                </div>
              </div>

              {/* Files list */}
              {files.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <FileText size={24} className="mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No files attached</p>
                  <button
                    onClick={onAddMoreFiles}
                    className="mt-2 text-orange-500 hover:text-orange-700 text-sm font-medium"
                  >
                    Upload files
                  </button>
                </div>
              ) : (
                files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 hover:bg-gray-50 border-b border-gray-100">
                    <div className="flex items-center gap-2 overflow-hidden">
                      <FileText size={16} className="text-gray-600 flex-shrink-0" />
                      <div className="flex flex-col overflow-hidden">
                        <span className="text-sm text-gray-700 truncate">
                          {file.name}
                        </span>

                        {/* Show status indicators */}
                        {file.uploading && (
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Loader2 size={10} className="text-orange-500 animate-spin" />
                            <span>Uploading...</span>
                          </div>
                        )}

                        {file.uploaded && (
                          <span className="text-xs text-green-600">Uploaded</span>
                        )}

                        {file.error && (
                          <span className="text-xs text-red-600">{file.error}</span>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => onRemoveFile(file)}
                      className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                      aria-label={`Remove file ${file.name}`}
                      disabled={file.uploading}
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))
              )}
            </div>
          )}

          {activeTab === 'figma' && (
            <div>
              {/* Figma header */}
              <div className="flex justify-between items-center p-3 border-b border-gray-100">
                <h4 className="text-sm font-medium text-gray-700">Figma Designs</h4>
                <div className="flex gap-2">
                  <button
                    onClick={onAddFigmaDesign}
                    className="text-orange-500 hover:text-orange-700 text-sm font-medium flex items-center gap-1"
                  >
                    <Plus size={12} />
                    Add Design
                  </button>

                  {(selectedFigmaFiles.length > 0 || selectedFigmaJsonFiles.length > 0) && (
                    <button
                      onClick={onClearFigmaFiles}
                      className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                    >
                      Clear All
                    </button>
                  )}
                </div>
              </div>

              {/* Figma content */}
              {isLoadingFigma ? (
                <div className="p-6 text-center">
                  <Loader2 size={24} className="mx-auto mb-2 text-orange-500 animate-spin" />
                  <p className="text-sm text-gray-600">Loading Figma designs...</p>
                </div>
              ) : figmaFiles.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <div className="w-12 h-12 mx-auto mb-2 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg font-bold text-gray-400">F</span>
                  </div>
                  <p className="text-sm">No Figma designs added</p>
                  <button
                    onClick={onAddFigmaDesign}
                    className="mt-2 text-orange-500 hover:text-orange-700 text-sm font-medium"
                  >
                    Add Figma Design
                  </button>
                </div>
              ) : (
                figmaFiles.map((design) => {
                  const isProcessing = processingDesigns.has(design.id);
                  const getStatusColor = (status) => {
                      switch (status) {
                        case 'completed': return 'bg-green-100 text-green-800';
                        case 'processing': return 'bg-primary-100 text-primary-800';
                        case 'processing_wait': return 'bg-purple-100 text-purple-800'; // ADD THIS
                        case 'partially_completed': return 'bg-yellow-100 text-yellow-800';
                        case 'failed': return 'bg-red-100 text-red-800';
                        default: return 'bg-gray-100 text-gray-800';
                      }
                    };


                  return (
                    <div key={design.id} className="border-b border-gray-100 last:border-b-0">
                      <div className="flex items-center justify-between p-3 hover:bg-gray-50">
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          <div className="w-8 h-8 bg-purple-100 rounded flex items-center justify-center flex-shrink-0">
                            <span className="text-xs font-bold text-purple-600">F</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {design.name}
                            </p>
                            <div className="flex items-center gap-2">
                                <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(design.status)}`}>
                                  {design.status?.replace('_', ' ')}
                                  {/* ADD THIS CONDITION: */}
                                  {design.status === 'processing_wait' && jsonProcessingStatus[design.id] && (
                                    <span className="ml-1 text-primary-800">
                                      ({jsonProcessingStatus[design.id].percentage}%)
                                    </span>
                                  )}
                                </span>
                                {design.completed_frames !== undefined && (
                                  <span className="text-xs text-gray-500">
                                    {design.completed_frames}/{design.total_frames} frames
                                  </span>
                                )}
                              </div>

                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {isProcessing && (
                            <button
                              onClick={() => onCheckStatus(design)}
                              className="text-primary hover:text-primary-700 text-xs"
                              title="Check status"
                            >
                              <Info size={14} />
                            </button>
                          )}
                          <button
                            onClick={() => onToggleFigmaDesign(design.id)}
                            className="text-gray-500 hover:text-gray-700"
                            aria-label={`${expandedFigmaDesigns.includes(design.id) ? 'Hide' : 'Show'} JSON files`}
                          >
                            {expandedFigmaDesigns.includes(design.id) ? '▼' : '▶'}
                          </button>
                        </div>
                      </div>

                      {/* JSON files list */}
                      {expandedFigmaDesigns.includes(design.id) && (
                        <div className="ml-4 border-l border-gray-200">
                          {isLoadingFigmaJson && figmaJsonFiles[design.id] === undefined ? (
                            <div className="p-3 text-center">
                              <Loader2 size={16} className="mx-auto text-orange-500 animate-spin" />
                              <p className="text-xs text-gray-500 mt-1">Loading JSON files...</p>
                            </div>
                          ) : figmaJsonFiles[design.id] && figmaJsonFiles[design.id].length > 0 ? (
                            figmaJsonFiles[design.id].map((jsonFile, index) => {
                              const fileKey = `${design.id}-${jsonFile.filename}`;
                              const selectedFile = selectedFigmaJsonFiles.find(f => f.fileKey === fileKey);
                              const isSelected = !!selectedFile;

                              return (
                                <div
                                  key={index}
                                  className="flex items-center justify-between p-2 pl-4 hover:bg-gray-50"
                                  onMouseEnter={() => handleJsonFileMouseEnter(jsonFile)}
                                  onMouseLeave={handleJsonFileMouseLeave}
                                >
                                  <div className="flex items-center gap-2 flex-1 min-w-0">
                                    <div className="w-4 h-4 bg-primary-100 rounded flex items-center justify-center flex-shrink-0">
                                      <span className="text-xs font-bold text-primary">J</span>
                                    </div>
                                    <div className="flex flex-col overflow-hidden">
                                      <div className="flex items-center gap-1">
                                        <span className="text-xs text-gray-700 truncate">
                                          {jsonFile.frame_name}
                                        </span>
                                        {jsonFile.image_url && (
                                          <div className="w-2 h-2 bg-primary-400 rounded-full flex-shrink-0" title="Has preview image" />
                                        )}
                                      </div>
                                      {jsonFile.frame_id && (
                                        <span className="text-xs text-gray-500">
                                          Frame: {jsonFile.frame_id}
                                        </span>
                                      )}

                                      {/* Show status indicators */}
                                      {selectedFile?.uploading && (
                                        <div className="flex items-center gap-1 text-xs text-gray-500">
                                          <Loader2 size={8} className="text-orange-500 animate-spin" />
                                          <span>Uploading...</span>
                                        </div>
                                      )}

                                      {selectedFile?.uploaded && (
                                        <span className="text-xs text-green-600">Uploaded</span>
                                      )}

                                      {selectedFile?.error && (
                                        <span className="text-xs text-red-600">{selectedFile.error}</span>
                                      )}
                                    </div>
                                  </div>
                                  <button
                                    onClick={() => onSelectFigmaJson(design.id, jsonFile)}
                                    disabled={selectedFile?.uploading}
                                    className={`px-2 py-1 text-xs font-medium rounded ${isSelected
                                        ? selectedFile?.uploaded
                                          ? 'bg-green-100 text-green-700 border border-green-200'
                                          : selectedFile?.error
                                            ? 'bg-red-100 text-red-700 border border-red-200'
                                            : 'bg-orange-100 text-orange-700 border border-orange-200'
                                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                      } ${selectedFile?.uploading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                  >
                                    {selectedFile?.uploading ? (
                                      <Loader2 size={10} className="animate-spin" />
                                    ) : selectedFile?.uploaded ? (
                                      '✓'
                                    ) : selectedFile?.error ? (
                                      '✗'
                                    ) : isSelected ? (
                                      '✓'
                                    ) : (
                                      '+'
                                    )}
                                  </button>
                                </div>
                              );
                            })
                          ) : (
                            <div className="p-3 text-center">
                              <p className="text-xs text-gray-500">No JSON files available</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          )}
        </div>
      </div>

      {/* Image Preview */}
      <ImagePreview
        imageUrl={hoveredImage}
        isVisible={!!hoveredImage}
        mousePosition={mousePosition}
      />
    </div>,
    document.body
  );
};

// Main ChatInput component
const ChatInput = ({
  isStopped,
  inputValue,
  setInputValue,
  handleSendMessage,
  isReady,
  textAreaRef,
  activeReplyTo,
  isAiTyping,
  wsConnection
}) => {
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [showAttachments, setShowAttachments] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedAttachments, setUploadedAttachments] = useState([]);
  const [isMultiline, setIsMultiline] = useState(false);
  const [figmaFiles, setFigmaFiles] = useState([]);
  const [selectedFigmaFiles, setSelectedFigmaFiles] = useState([]);
  const [isLoadingFigma, setIsLoadingFigma] = useState(false);
  const [figmaFilesLoaded, setFigmaFilesLoaded] = useState(false);
  const [expandedFigmaDesigns, setExpandedFigmaDesigns] = useState([]);
  const [figmaJsonFiles, setFigmaJsonFiles] = useState({});
  const [selectedFigmaJsonFiles, setSelectedFigmaJsonFiles] = useState([]);
  const [isLoadingFigmaJson, setIsLoadingFigmaJson] = useState(false);
  const [uploadedJsonAttachments, setUploadedJsonAttachments] = useState([]);
  const [jsonProcessingStatus, setJsonProcessingStatus] = useState({});
  // New state for Figma design addition
  const [showAddFigmaModal, setShowAddFigmaModal] = useState(false);
  const [isAddingFigma, setIsAddingFigma] = useState(false);
  const [processingDesigns, setProcessingDesigns] = useState(new Set());
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [currentProgressDesign, setCurrentProgressDesign] = useState(null);
  const [processingStatus, setProcessingStatus] = useState(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  

  
  const searchParams = useSearchParams();
  const [isStop, setIsStop] = useState(false);
  const fileInputRef = useRef(null);
  const plusButtonRef = useRef(null);
  const wsRef = useRef(null);

  // Get project ID from URL
  const { projectId } = useParams();

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (projectId) {
      connectWebSocket();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [projectId]);

  const connectWebSocket = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN ||
        wsRef.current?.readyState === WebSocket.CONNECTING) {
      return;
    }

    const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/figma-${projectId}`);
    wsRef.current = ws;

    ws.onopen = () => {
      ws.send(JSON.stringify({
        type: "client",
        task_id: `figma-${projectId}`
      }));
    };

  ws.onmessage = (event) => {
  try {
    const message = JSON.parse(event.data);
    if (message.type === 'figma_update') {
      const { figma_id, update_data } = message.data;
      
      // Update figma files list
      setFigmaFiles(prevFiles => 
        prevFiles.map(file => 
          file.id === figma_id 
            ? { ...file, ...update_data }
            : file
        )
      );

      // Update processing status if modal is open for this design
      if (currentProgressDesign?.id === figma_id) {
        setProcessingStatus(prev => ({
          ...prev,
          ...update_data
        }));
      }

      // If status is completed, refresh the entire figma files list
      if (update_data.status === 'completed') {
        // Add a small delay to ensure backend has processed everything
        setTimeout(() => {
          refreshFigmaFiles();
        }, 2000);
        
        setProcessingDesigns(prev => {
          const newSet = new Set(prev);
          newSet.delete(figma_id);
          return newSet;
        });
      }
      
      // If status is failed, just remove from processing
      if (update_data.status === 'failed') {
        setProcessingDesigns(prev => {
          const newSet = new Set(prev);
          newSet.delete(figma_id);
          return newSet;
        });
      }
    }
    else if (message.type === 'figma_update_json') {
      const { figma_id, update_data } = message.data;
      
      // Update JSON processing status
      setJsonProcessingStatus(prev => ({
        ...prev,
        [figma_id]: {
          processed_count: update_data.processed_count,
          total_count: update_data.total_count,
          percentage: update_data.percentage,
          time_updated: update_data.time_updated
        }
      }));
    }
  } catch (error) {
    console.error('WebSocket message error:', error);
  }
};

    ws.onclose = () => {
      setTimeout(() => {
        if (processingDesigns.size > 0) {
          connectWebSocket();
        }
      }, 3000);
    };
  };

  // Load Figma files when component mounts
  useEffect(() => {
    const loadFigmaFiles = async () => {
      if (!projectId || figmaFilesLoaded) return;

      setIsLoadingFigma(true);
      try {
        const response = await getFigmaFiles(projectId);
        setFigmaFiles(response.designs || []);
        setFigmaFilesLoaded(true);
      } catch (error) {
        console.error('Error loading Figma files:', error);
        setFigmaFiles([]);
      } finally {
        setIsLoadingFigma(false);
      }
    };

    loadFigmaFiles();
  }, [projectId, figmaFilesLoaded]);

  // Handle adding new Figma design
  const handleAddFigmaDesign = () => {
    setShowAddFigmaModal(true);
  };

  // Handle Figma design submission
  const handleFigmaDesignSubmit = async (designName, figmaLink) => {
    setIsAddingFigma(true);
    try {
      const response = await addFigmaFileV2(projectId, designName, figmaLink);
      
      if (response.status === 'pending') {
        // Add to processing designs
        setProcessingDesigns(prev => new Set(prev).add(response.id));
        
        // Add to figma files list
        const newDesign = {
          id: response.id,
          name: designName,
          url: figmaLink,
          status: 'pending',
          completed_frames: 0,
          total_frames: 0,
          failed_frames: 0,
          time_created: new Date().toISOString(),
          added_by: { name: 'You' }
        };
        
        setFigmaFiles(prev => [newDesign, ...prev]);
        setShowAddFigmaModal(false);
        
        // Show success message
        console.log('Figma design added successfully');
        
        // Ensure WebSocket connection is established
        if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
          connectWebSocket();
        }
      } else {
        console.error('Failed to add Figma design:', response.message);
      }
    } catch (error) {
      console.error('Error adding Figma design:', error);
    } finally {
      setIsAddingFigma(false);
    }
  };

  // Handle checking status
  const handleCheckStatus = async (design) => {
    setCurrentProgressDesign(design);
    setShowProgressModal(true);
    setIsLoadingStatus(true);
    
    try {
      const status = await getFigmaProcessingStatus(projectId, design.id);
      setProcessingStatus(status);
    } catch (error) {
      console.error('Error fetching status:', error);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  // Handle Figma file selection
  const handleSelectFigmaFile = (design, clearAll = false) => {
    if (clearAll) {
      setSelectedFigmaFiles([]);
      return;
    }

    setSelectedFigmaFiles(prev => {
      const isSelected = prev.some(f => f.id === design.id);
      if (isSelected) {
        return prev.filter(f => f.id !== design.id);
      } else {
        return [...prev, design];
      }
    });
  };

  const handleClearFigmaFiles = () => {
    setSelectedFigmaFiles([]);
    setSelectedFigmaJsonFiles([]);
    setUploadedJsonAttachments([]);
  };

  
// Handle Figma design expansion/collapse
const handleToggleFigmaDesign = async (designId) => {
  setExpandedFigmaDesigns(prev => {
    const isExpanded = prev.includes(designId);
    if (isExpanded) {
      return prev.filter(id => id !== designId);
    } else {
      // Load JSON files if not already loaded
      const design = figmaFiles.find(f => f.id === designId);
      if (design && design.file_key && !figmaJsonFiles[designId]) {
        loadFigmaJsonFiles(designId, design.file_key);
      }
      return [...prev, designId];
    }
  });
};
  // Load JSON files for a specific Figma design
  const loadFigmaJsonFiles = async (designId, fileKey) => {
    if (!projectId || !fileKey) return;

    setIsLoadingFigmaJson(true);
    try {
      const response = await getFigmaJsonFiles(projectId, fileKey);
      setFigmaJsonFiles(prev => ({
        ...prev,
        [designId]: response.files || []
      }));
    } catch (error) {
      console.error('Error loading Figma JSON files:', error);
      setFigmaJsonFiles(prev => ({
        ...prev,
        [designId]: []
      }));
    } finally {
      setIsLoadingFigmaJson(false);
    }
  };
  
  // // Refresh figma files list
const refreshFigmaFiles = async () => {
  if (!projectId) return;
  
  try {
    const response = await getFigmaFiles(projectId);
    setFigmaFiles(response.designs || []);
    // Clear JSON files cache so they get reloaded with updated file_key
    setFigmaJsonFiles({});
  } catch (error) {
    console.error('Error refreshing Figma files:', error);
  }
};

  // Handle Figma JSON file selection
  const handleSelectFigmaJson = async (designId, jsonFile, clearAll = false) => {
    if (clearAll) {
      setSelectedFigmaJsonFiles([]);
      setUploadedJsonAttachments([]);
      return;
    }

    const fileKey = `${designId}-${jsonFile.filename}`;
    const isSelected = selectedFigmaJsonFiles.some(f => f.fileKey === fileKey);

    if (isSelected) {
      // Remove from selection
      setSelectedFigmaJsonFiles(prev => prev.filter(f => f.fileKey !== fileKey));
      setUploadedJsonAttachments(prev => prev.filter(f => f.fileKey !== fileKey));
    } else {
      // Add to selection with uploading status
      const jsonFileObj = {
        fileKey,
        designId,
        filename: jsonFile.filename,
        path: jsonFile.path,
        relativePath: jsonFile.relative_path,
        uploading: true,
        uploaded: false,
        error: null
      };

      setSelectedFigmaJsonFiles(prev => [...prev, jsonFileObj]);

      // Start upload process
      try {
        // Upload using file path approach
        const uploadResult = await uploadMultipleAttachments([{
          path: jsonFile.path,
          filename: jsonFile.filename
        }], projectId);

        if (uploadResult[0] && uploadResult[0].success !== false) {
          // Update status to uploaded
          setSelectedFigmaJsonFiles(prev => prev.map(f =>
            f.fileKey === fileKey
              ? { ...f, uploading: false, uploaded: true, attachmentId: uploadResult[0].attachment_id }
              : f
          ));

          // Add to uploaded attachments
          setUploadedJsonAttachments(prev => [...prev, {
            fileKey,
            attachment_id: uploadResult[0].attachment_id,
            filename: uploadResult[0].filename,
            file_location: uploadResult[0].file_location || uploadResult[0].path,
            size: uploadResult[0].size
          }]);
        } else {
          // Handle upload failure
          setSelectedFigmaJsonFiles(prev => prev.map(f =>
            f.fileKey === fileKey
              ? { ...f, uploading: false, error: uploadResult[0]?.error || "Upload failed" }
              : f
          ));
        }
      } catch (error) {
        console.error('Error uploading JSON file:', error);
        setSelectedFigmaJsonFiles(prev => prev.map(f =>
          f.fileKey === fileKey
            ? { ...f, uploading: false, error: "Upload failed" }
            : f
        ));
      }
    }
  };

  // Handle file selection
  const handleFileSelect = async (e) => {
    if (e.target.files.length > 0) {
      const newFiles = [...attachedFiles];
      const filesToUpload = [];

      // First, add files to state with uploading status
      for (let i = 0; i < e.target.files.length; i++) {
        const currentFile = e.target.files[i];

        // Check if file already exists in array (by name and size)
        const isDuplicate = newFiles.some(
          existingFile =>
            existingFile.name === currentFile.name &&
            existingFile.size === currentFile.size
        );

        if (!isDuplicate) {
          // Add file with uploading status
          const fileObj = {
            name: currentFile.name,
            size: currentFile.size,
            uploaded: false,
            uploading: true,
            file: currentFile
          };

          newFiles.push(fileObj);
          filesToUpload.push(fileObj);
        }
      }

      // Update state to show files with loading state
      setAttachedFiles(newFiles);
      setShowAttachments(true);

      // Start upload immediately if we have projectId
      if (projectId && filesToUpload.length > 0) {
        setIsUploading(true);

        try {
          const files = filesToUpload.map(fileObj => fileObj.file);
          const results = await uploadMultipleAttachments(files, projectId);

          setAttachedFiles(prev => {
            const updatedFiles = [...prev];

            results.forEach((result, index) => {
              if (result.success !== false) {
                const fileIndex = updatedFiles.findIndex(
                  f => f.name === files[index].name && f.size === files[index].size
                );

                if (fileIndex !== -1) {
                  updatedFiles[fileIndex].uploaded = true;
                  updatedFiles[fileIndex].uploading = false;
                  updatedFiles[fileIndex].attachmentId = result.attachment_id;
                }
              } else {
                const fileIndex = updatedFiles.findIndex(
                  f => f.name === files[index].name && f.size === files[index].size
                );

                if (fileIndex !== -1) {
                  updatedFiles[fileIndex].uploading = false;
                  updatedFiles[fileIndex].error = result.error || "Upload failed";
                }
              }
            });

            return updatedFiles;
          });

          const newUploadedAttachments = results
            .filter(result => result.success !== false)
            .map(result => ({
              attachment_id: result.attachment_id,
              filename: result.filename,
              file_location: result.file_location,
              size: result.size
            }));

          setUploadedAttachments(prev => [...prev, ...newUploadedAttachments]);
        } catch (error) {
          setAttachedFiles(prev => {
            return prev.map(f => {
              if (f.uploading) {
                return { ...f, uploading: false, error: "Upload failed" };
              }
              return f;
            });
          });
        } finally {
          setIsUploading(false);
        }
      }
    }
  };

  // Handle removing a file
  const handleRemoveFile = (fileToRemove) => {
    if (fileToRemove.uploading) return;

    const updatedFiles = attachedFiles.filter(file => file !== fileToRemove);
    setAttachedFiles(updatedFiles);

    if (fileToRemove.attachmentId) {
      setUploadedAttachments(prev => prev.filter(
        attachment => attachment.attachment_id !== fileToRemove.attachmentId
      ));
    }

    if (updatedFiles.length === 0 && selectedFigmaFiles.length === 0 && selectedFigmaJsonFiles.length === 0) {
      setShowAttachments(false);
    }
  };

  // Handle clearing all files
  const handleClearFiles = () => {
    setAttachedFiles([]);
    setUploadedAttachments([]);

    if (selectedFigmaFiles.length === 0 && selectedFigmaJsonFiles.length === 0) {
      setShowAttachments(false);
    }
  };

  // Handle the Plus button click
  const handlePlusClick = () => {
    if (attachedFiles.length > 0 || selectedFigmaFiles.length > 0 || selectedFigmaJsonFiles.length > 0) {
      setShowAttachments(!showAttachments);
    } else {
      setShowAttachments(true);
    }
  };

  // Handle Add More Files button in popup
  const handleAddMoreFiles = () => {
    setTimeout(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }, 10);
  };

  // Close popup
  const handleClosePopup = () => {
    setShowAttachments(false);
  };

  // Upload files before sending message
  const uploadFiles = async () => {
    if (!projectId || attachedFiles.length === 0) return [];

    const filesToUpload = attachedFiles.filter(file => !file.uploaded);
    if (filesToUpload.length === 0) return uploadedAttachments;

    setIsUploading(true);

    try {
      const files = filesToUpload.map(fileObj => fileObj.file);
      const results = await uploadMultipleAttachments(files, projectId);

      const updatedFiles = [...attachedFiles];
      results.forEach((result, index) => {
        if (result.success !== false) {
          const fileIndex = updatedFiles.findIndex(
            f => f.name === files[index].name && f.size === files[index].size
          );

          if (fileIndex !== -1) {
            updatedFiles[fileIndex].uploaded = true;
            updatedFiles[fileIndex].attachmentId = result.attachment_id;
          }
        }
      });

      setAttachedFiles(updatedFiles);

      const newUploadedAttachments = results
        .filter(result => result.success !== false)
        .map(result => ({
          attachment_id: result.attachment_id,
          filename: result.filename,
          file_location: result.file_location,
          size: result.size
        }));

      setUploadedAttachments(prev => [...prev, ...newUploadedAttachments]);
      return newUploadedAttachments;
    } catch (error) {
      return [];
    } finally {
      setIsUploading(false);
    }
  };

  // Adjust textarea height
  const adjustTextAreaHeight = (element) => {
    if (!element) return;

    if (!element.value.trim()) {
      element.style.height = '36px';
      if (isMultiline) setIsMultiline(false);
      return;
    }

    const scrollPosition = element.scrollTop;
    const selectionStart = element.selectionStart;
    const selectionEnd = element.selectionEnd;

    element.style.height = '36px';

    const contentHeight = element.scrollHeight;
    const minHeight = 36;
    const maxHeight = 140;

    const newHeight = Math.min(Math.max(contentHeight, minHeight), maxHeight);
    element.style.height = `${newHeight}px`;

    element.style.overflowY = contentHeight > maxHeight ? 'auto' : 'hidden';

    const shouldBeMultiline = contentHeight > (minHeight + 14);
    if (isMultiline !== shouldBeMultiline) {
      setIsMultiline(shouldBeMultiline);
    }

    element.selectionStart = selectionStart;
    element.selectionEnd = selectionEnd;
    element.scrollTop = scrollPosition;
  };

  // Check if send button should be enabled
  const isSendEnabled = (
    inputValue.trim().length > 0 ||
    attachedFiles.length > 0 ||
    selectedFigmaFiles.length > 0 ||
    selectedFigmaJsonFiles.filter(f => f.uploaded).length > 0
  ) && !isUploading && !selectedFigmaJsonFiles.some(f => f.uploading);

  // Modified handleSendClick function
  const handleSendClick = async () => {
    if (!isSendEnabled) return;

    try {
      let finalAttachments = [...uploadedAttachments];

      if (attachedFiles.some(file => !file.uploaded)) {
        const newAttachments = await uploadFiles();
        finalAttachments = [...uploadedAttachments, ...newAttachments];
      }

      const messageData = {
        type: 'send_message',
        content: inputValue.trim(),
        parent_id: activeReplyTo,
      };

      // Get uploaded JSON files
      const uploadedJsonFiles = uploadedJsonAttachments.filter(f =>
        selectedFigmaJsonFiles.some(sf => sf.fileKey === f.fileKey && sf.uploaded)
      );

      // Standardize JSON file format using the existing designId
      const standardizedJsonFiles = uploadedJsonFiles.map(f => {
        // Find the corresponding selected file to get the designId
        const selectedFile = selectedFigmaJsonFiles.find(sf => sf.fileKey === f.fileKey);
        
        return {
          attachment_id: f.attachment_id,
          filename: f.filename,
          size: f.size,
          fileKey: selectedFile.designId,
          file_type: "figma_json"
        };
      });

      if (standardizedJsonFiles.length > 0) {
        finalAttachments = [...finalAttachments, ...standardizedJsonFiles];
      }

      // Add all attachments (regular files + JSON files) if any
      if (finalAttachments.length > 0) {
        messageData.attachment_ids = finalAttachments.map(a => a.attachment_id);
        messageData.attachments = finalAttachments;
      }

      // Add Figma files if any (design metadata only)
      if (selectedFigmaFiles.length > 0) {
        messageData.figma_files = selectedFigmaFiles.map(f => ({
          id: f.id,
          name: f.name,
          file_key: f.file_key,
          url: f.url,
          file_type: "figma_design"
        }));
      }

      // Send the message...
      if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(messageData));
      } else {
        handleSendMessage();
      }

      // Clear everything after sending...
      setAttachedFiles([]);
      setUploadedAttachments([]);
      setSelectedFigmaFiles([]);
      setSelectedFigmaJsonFiles([]);
      setUploadedJsonAttachments([]);
      setShowAttachments(false);
      setInputValue('');
      setIsMultiline(false);

      if (textAreaRef.current) {
        setTimeout(() => {
          if (textAreaRef.current) {
            textAreaRef.current.style.height = '36px';
            textAreaRef.current.style.overflowY = 'hidden';
            adjustTextAreaHeight(textAreaRef.current);
            textAreaRef.current.focus();
          }
        }, 0);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Focus textarea when component mounts
  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = '36px';
      textAreaRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isAiTyping == false) {
      setIsStop(false);
    }
  }, [isAiTyping]);

  const totalAttachments = attachedFiles.length + selectedFigmaJsonFiles.length;

  return (
    <div className="w-full max-w-lg mx-auto rounded-xl bg-white shadow-[0px_2px_4px_-2px_rgba(0,0,0,0.05),0px_-1px_5px_0px_rgba(0,0,0,0.05)] outline outline-1 outline-offset-[-1px] outline-[#f26a1b]/50 overflow-hidden relative p-0.5">
      <style dangerouslySetInnerHTML={{
        __html: `
        textarea.chat-input {
          caret-color: rgba(107, 114, 128, 0.5);
          caret-width: thin;
        }
        textarea.chat-input::selection {
          background-color: rgba(59, 130, 246, 0.2);
        }
      `}} />
        <textarea
          ref={textAreaRef}
          value={inputValue}
          placeholder={isReady
            ? (isAiTyping
              ? isStop
                ? "Stopping response..." : "Generating your response, please wait..."
              : "Reply to message...")
            : "Reply to message..."}
          className="chat-input w-full resize-none border-none bg-transparent text-gray-700 placeholder-gray-400 font-weight-medium leading-tight focus:outline-none focus:ring-0"
          style={{
            height: '43px',
            maxHeight: '150px',
            overflowY: 'hidden',
            minHeight: '43px',
            paddingRight: '32px',
            paddingLeft: '28px',
            paddingTop: '14px',
            paddingBottom: '14px',
            cursor: (isUploading || isStopped || isAiTyping) ? 'not-allowed' : 'text'
          }}
          rows={1}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              if (isSendEnabled) {
                handleSendClick();
              }
            }
          }}
          onChange={(e) => {
            setInputValue(e.target.value);
            adjustTextAreaHeight(e.target);
          }}
          disabled={isUploading || isStopped || isAiTyping}
        />

        {/* Plus button */}
        <div className={`absolute ${isMultiline ? 'bottom-2' : 'top-1/2 -translate-y-1/2'} left-2`}>
            <button
              ref={plusButtonRef}
              onClick={handlePlusClick}
              className={`size-6 bg-white rounded-full outline outline-1 outline-offset-[-1px] outline-gray-200 flex items-center justify-center hover:bg-gray-50 relative ${isUploading || isAiTyping ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              aria-label="Add attachments"
              aria-haspopup="dialog"
              aria-expanded={showAttachments}
              disabled={isUploading || isAiTyping}
            >
              {isUploading ? (
                <Loader2 size={16} className="text-gray-600 animate-spin" />
              ) : (
                <Plus size={16} className="text-gray-600" />
              )}

              {/* Badge showing total attachments */}
              {totalAttachments > 0 && (
                <div className="absolute -top-1.5 -right-1.5 flex items-center justify-center min-w-[14px] h-[14px] text-[9px] font-weight-semibold text-white bg-orange-500 rounded-full px-0.5">
                  {totalAttachments > 99 ? '99+' : totalAttachments}
                </div>
              )}
            </button>
        </div>

        {/* Send button */}
        <div className={`absolute ${isMultiline ? 'bottom-2 right-2' : 'top-1/2 -translate-y-1/2 right-2'}`}>
            <BootstrapTooltip
              title={
                isUploading
                  ? "Uploading files..."
                  : isAiTyping
                    ? (isStop ? "Stopping response..." : "Stop response")
                    : "Send message"
              }
              placement="top"
            >
              <button
                onClick={isAiTyping ? () => {
                  setIsStop(true)
                  if (wsConnection?.readyState === WebSocket.OPEN) {
                    wsConnection.send(
                      JSON.stringify({
                        type: "stop_streaming",
                        task_id: searchParams.get("task_id"),
                      })
                    );
                  }
                  setInputValue(" ");
                } : handleSendClick}
                disabled={isAiTyping ? !isReady || isStop : (!isSendEnabled || !isReady)}
                className={`size-6 rounded-full flex items-center justify-center ${isAiTyping
                    ? isStop
                      ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                      : "bg-red-500 text-white hover:bg-red-600"
                    : isSendEnabled && isReady
                      ? "bg-orange-500 text-white hover:bg-orange-600"
                      : "bg-gray-200 text-gray-400 cursor-not-allowed"
                  }`}
                aria-label={isAiTyping ? isStop ? "Stopping response..." : "Stop response" : "Send message"}
              >
                {isUploading ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : isAiTyping ? (
                  <FaStopCircle size={20} className="" />
                ) : (
                  <ArrowUp size={16} />
                )}
              </button>
            </BootstrapTooltip>
        </div>

        {/* Hidden file input */}
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          multiple
          onChange={handleFileSelect}
          aria-hidden="true"
          disabled={isUploading}
          accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt,.md,.json,.csv,.ppt,.pptx"
          onClick={(e) => {
            e.currentTarget.value = '';
          }}
        />

        {/* File attachment popup with portal */}
        {showAttachments && (
          <FileAttachmentPopup
            files={attachedFiles}
            onRemoveFile={handleRemoveFile}
            onClearFiles={handleClearFiles}
            onAddMoreFiles={handleAddMoreFiles}
            onClose={handleClosePopup}
            triggerButtonRef={plusButtonRef}
            isUploading={isUploading}
            figmaFiles={figmaFiles}
            onSelectFigmaFile={handleSelectFigmaFile}
            selectedFigmaFiles={selectedFigmaFiles}
            isLoadingFigma={isLoadingFigma}
            expandedFigmaDesigns={expandedFigmaDesigns}
            onToggleFigmaDesign={handleToggleFigmaDesign}
            figmaJsonFiles={figmaJsonFiles}
            onSelectFigmaJson={handleSelectFigmaJson}
            selectedFigmaJsonFiles={selectedFigmaJsonFiles}
            isLoadingFigmaJson={isLoadingFigmaJson}
            onClearFigmaFiles={handleClearFigmaFiles}
            onAddFigmaDesign={handleAddFigmaDesign}
            processingDesigns={processingDesigns}
            onCheckStatus={handleCheckStatus}
            jsonProcessingStatus={jsonProcessingStatus}
          />
        )}

        {/* Add Figma Design Modal */}
        <AddFigmaDesignModal
          isOpen={showAddFigmaModal}
          onClose={() => setShowAddFigmaModal(false)}
          onSubmit={handleFigmaDesignSubmit}
          isLoading={isAddingFigma}
        />

        {/* Progress Modal */}
        <ProgressModal
          isOpen={showProgressModal}
          onClose={() => setShowProgressModal(false)}
          design={currentProgressDesign}
          processingStatus={processingStatus}
          isLoadingStatus={isLoadingStatus}
        />
      </div>
  );
};

export default ChatInput;